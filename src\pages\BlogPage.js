import React from 'react';

const BlogPage = () => {
  const containerStyle = {
    padding: '80px 60px',
    minHeight: '80vh',
    backgroundColor: '#f8f9fa'
  };

  const titleStyle = {
    fontSize: '48px',
    fontWeight: 'bold',
    color: '#1a3d2e',
    marginBottom: '30px',
    textAlign: 'center'
  };

  const contentStyle = {
    fontSize: '18px',
    color: '#666',
    textAlign: 'center',
    maxWidth: '600px',
    margin: '0 auto'
  };

  return (
    <div style={containerStyle}>
      <h1 style={titleStyle}>BLOG</h1>
      <p style={contentStyle}>
        Stay updated with the latest sports news, tips, and insights from our expert team. 
        Discover training techniques, equipment reviews, and inspiring stories from athletes around the world.
      </p>
    </div>
  );
};

export default BlogPage;
