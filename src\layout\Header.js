import React from 'react';
import { Link } from 'react-router-dom';

const Header = () => {
  const headerStyle = {
    backgroundColor: '#ffffff',
    padding: '15px 50px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    position: 'relative',
    zIndex: 1000
  };

  const logoStyle = {
    fontSize: '24px',
    fontWeight: 'bold',
    color: '#2d5016',
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center'
  };

  const logoTextStyle = {
    display: 'flex',
    flexDirection: 'column',
    lineHeight: '1'
  };

  const navStyle = {
    display: 'flex',
    listStyle: 'none',
    margin: 0,
    padding: 0,
    gap: '30px'
  };

  const navLinkStyle = {
    textDecoration: 'none',
    color: '#333',
    fontSize: '14px',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: '1px',
    transition: 'color 0.3s ease'
  };

  const navLinkHoverStyle = {
    color: '#7cb342'
  };

  const iconsStyle = {
    display: 'flex',
    gap: '15px',
    alignItems: 'center'
  };

  const iconStyle = {
    width: '20px',
    height: '20px',
    cursor: 'pointer',
    transition: 'transform 0.3s ease'
  };

  return (
    <header style={headerStyle}>
      {/* Logo */}
      <Link to="/" style={logoStyle}>
        <div style={logoTextStyle}>
          <span>HA</span>
          <span>SPORT</span>
        </div>
      </Link>

      {/* Navigation */}
      <nav>
        <ul style={navStyle}>
          <li>
            <Link 
              to="/" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              HOME
            </Link>
          </li>
          <li>
            <Link 
              to="/catalog" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              CATALOG
            </Link>
          </li>
          <li>
            <Link 
              to="/blog" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              BLOG
            </Link>
          </li>
          <li>
            <Link 
              to="/shop" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              SHOP
            </Link>
          </li>
          <li>
            <Link 
              to="/contact" 
              style={navLinkStyle}
              onMouseEnter={(e) => e.target.style.color = navLinkHoverStyle.color}
              onMouseLeave={(e) => e.target.style.color = navLinkStyle.color}
            >
              CONTACT US
            </Link>
          </li>
        </ul>
      </nav>

      {/* Icons */}
      <div style={iconsStyle}>
        {/* Search Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
        
        {/* Heart Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
        
        {/* User Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
        
        {/* Cart Icon */}
        <svg style={iconStyle} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="9" cy="21" r="1"></circle>
          <circle cx="20" cy="21" r="1"></circle>
          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
        </svg>
      </div>
    </header>
  );
};

export default Header;
